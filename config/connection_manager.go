package config

import (
	"context"
	"log"
	"math"
	"sync"
	"time"

	MQTT "github.com/eclipse/paho.mqtt.golang"
)

// ConnectionManager manages MQTT connection lifecycle with monitoring and reconnection
type ConnectionManager struct {
	client             MQTT.Client
	ctx                context.Context
	cancel             context.CancelFunc
	reconnectAttempts  int
	maxReconnectDelay  time.Duration
	baseReconnectDelay time.Duration
	mu                 sync.RWMutex
	connected          bool
	connectionLost     chan struct{}
	reconnected        chan struct{}
}

// NewConnectionManager creates a new connection manager
func NewConnectionManager(ctx context.Context) *ConnectionManager {
	childCtx, cancel := context.WithCancel(ctx)

	return &ConnectionManager{
		ctx:                childCtx,
		cancel:             cancel,
		maxReconnectDelay:  10 * time.Minute,
		baseReconnectDelay: 1 * time.Second,
		connectionLost:     make(chan struct{}, 1),
		reconnected:        make(chan struct{}, 1),
	}
}

// Connect establishes MQTT connection with enhanced monitoring
func (cm *ConnectionManager) Connect() error {
	client, err := CreateClient(cm.ctx)
	if err != nil {
		return err
	}

	cm.mu.Lock()
	cm.client = client
	cm.connected = true
	cm.reconnectAttempts = 0
	cm.mu.Unlock()

	// Start connection monitoring
	go cm.monitorConnection()

	return nil
}

// GetClient returns the MQTT client (thread-safe)
func (cm *ConnectionManager) GetClient() MQTT.Client {
	cm.mu.RLock()
	defer cm.mu.RUnlock()
	return cm.client
}

// IsConnected returns connection status (thread-safe)
func (cm *ConnectionManager) IsConnected() bool {
	cm.mu.RLock()
	defer cm.mu.RUnlock()
	return cm.connected && cm.client != nil && cm.client.IsConnected()
}

// Disconnect gracefully disconnects the MQTT client
func (cm *ConnectionManager) Disconnect() {
	cm.cancel() // Cancel context to stop monitoring

	cm.mu.Lock()
	defer cm.mu.Unlock()

	if cm.client != nil && cm.client.IsConnected() {
		log.Println("Disconnecting MQTT client...")
		cm.client.Disconnect(5000)
		log.Println("MQTT client disconnected")
	}
	cm.connected = false
}

// monitorConnection monitors the connection and handles reconnection with exponential backoff
func (cm *ConnectionManager) monitorConnection() {
	ticker := time.NewTicker(30 * time.Second) // Check connection every 30 seconds
	defer ticker.Stop()

	for {
		select {
		case <-cm.ctx.Done():
			log.Println("Connection monitoring stopped")
			return

		case <-ticker.C:
			if !cm.IsConnected() {
				log.Println("MQTT connection lost, attempting to reconnect...")
				cm.handleReconnection()
			}

		case <-cm.connectionLost:
			log.Println("Connection lost signal received, attempting to reconnect...")
			cm.handleReconnection()
		}
	}
}

// handleReconnection handles reconnection with exponential backoff
func (cm *ConnectionManager) handleReconnection() {
	cm.mu.Lock()
	cm.connected = false
	cm.reconnectAttempts++
	attempts := cm.reconnectAttempts
	cm.mu.Unlock()

	// Calculate backoff delay with exponential backoff and jitter
	delay := time.Duration(math.Min(
		float64(cm.baseReconnectDelay)*math.Pow(2, float64(attempts-1)),
		float64(cm.maxReconnectDelay),
	))

	// Add jitter (±25% of delay)
	jitterFactor := float64(2*time.Now().UnixNano()%2-1) / 1e9
	jitter := time.Duration(float64(delay) * 0.25 * jitterFactor)
	delay += jitter

	log.Printf("Reconnection attempt %d in %v", attempts, delay)

	select {
	case <-cm.ctx.Done():
		return
	case <-time.After(delay):
		// Attempt reconnection
		if err := cm.attemptReconnection(); err != nil {
			log.Printf("Reconnection attempt %d failed: %v", attempts, err)
		} else {
			log.Printf("Reconnection attempt %d successful", attempts)
			cm.mu.Lock()
			cm.connected = true
			cm.reconnectAttempts = 0
			cm.mu.Unlock()

			// Notify reconnection
			select {
			case cm.reconnected <- struct{}{}:
			default:
			}
		}
	}
}

// attemptReconnection attempts to reconnect to MQTT broker
func (cm *ConnectionManager) attemptReconnection() error {
	client, err := CreateClient(cm.ctx)
	if err != nil {
		return err
	}

	cm.mu.Lock()
	// Disconnect old client if it exists
	if cm.client != nil {
		cm.client.Disconnect(1000)
	}
	cm.client = client
	cm.mu.Unlock()

	return nil
}

// ConnectionLost returns a channel that signals when connection is lost
func (cm *ConnectionManager) ConnectionLost() <-chan struct{} {
	return cm.connectionLost
}

// Reconnected returns a channel that signals when reconnection is successful
func (cm *ConnectionManager) Reconnected() <-chan struct{} {
	return cm.reconnected
}

// NotifyConnectionLost manually triggers connection lost handling
func (cm *ConnectionManager) NotifyConnectionLost() {
	select {
	case cm.connectionLost <- struct{}{}:
	default:
	}
}
