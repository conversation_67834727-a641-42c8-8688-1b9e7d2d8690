package config

import (
	"fmt"

	"github.com/joho/godotenv"
	"github.com/tkanos/gonfig"
)

type Configuration struct {
	// MQTT Configuration
	MQTTServer    string `env:"MQTT_SERVER"`
	MQTTPort      string `env:"MQTT_PORT"`
	MQTTClientID  string `env:"MQTT_CLIENT_ID"`
	MQTTUsername  string `env:"MQTT_USERNAME"`
	MQTTPassword  string `env:"MQTT_PASSWORD"`
	MQTTEnableTLS string `env:"MQTT_ENABLE_TLS"` // "true" or "false"
	MQTTQoS       string `env:"MQTT_QOS"`        // "0", "1", or "2"

	// Database Configuration
	DbUsername string `env:"DB_USERNAME"`
	DbPassword string `env:"DB_PASSWORD"`
	DbName     string `env:"DB_NAME"`
	DbPort     string `env:"DB_PORT"`
	DbHost     string `env:"DB_HOST"`
}

// Get configuration values from env file with validation and defaults
func GetConfig() Configuration {
	err := godotenv.Load()
	if err != nil {
		fmt.Println("Warning: Error loading .env file:", err)
	}

	configuration := Configuration{}

	err = gonfig.GetConf("", &configuration)
	if err != nil {
		fmt.Println("Error loading config:", err)
	}

	// Apply defaults and validate
	configuration = applyDefaults(configuration)

	return configuration
}

// applyDefaults sets default values for missing configuration
func applyDefaults(cfg Configuration) Configuration {
	// MQTT defaults
	if cfg.MQTTPort == "" {
		cfg.MQTTPort = "1883" // Default MQTT port
	}
	if cfg.MQTTEnableTLS == "" {
		cfg.MQTTEnableTLS = "false"
	}
	if cfg.MQTTQoS == "" {
		cfg.MQTTQoS = "1" // At least once delivery
	}

	// Database defaults
	if cfg.DbPort == "" {
		cfg.DbPort = "5432" // Default PostgreSQL port
	}
	if cfg.DbHost == "" {
		cfg.DbHost = "localhost"
	}

	return cfg
}

// ValidateConfig validates the configuration values
func ValidateConfig(cfg Configuration) error {
	// Validate MQTT configuration
	if cfg.MQTTServer == "" {
		return fmt.Errorf("MQTT_SERVER is required")
	}
	if cfg.MQTTClientID == "" {
		return fmt.Errorf("MQTT_CLIENT_ID is required")
	}

	// Validate QoS level
	switch cfg.MQTTQoS {
	case "0", "1", "2":
		// Valid QoS levels
	default:
		return fmt.Errorf("invalid MQTT_QOS value: %s (must be 0, 1, or 2)", cfg.MQTTQoS)
	}

	// Validate TLS setting
	switch cfg.MQTTEnableTLS {
	case "true", "false":
		// Valid boolean values
	default:
		return fmt.Errorf("invalid MQTT_ENABLE_TLS value: %s (must be true or false)", cfg.MQTTEnableTLS)
	}

	// Validate database configuration
	if cfg.DbUsername == "" {
		return fmt.Errorf("DB_USERNAME is required")
	}
	if cfg.DbPassword == "" {
		return fmt.Errorf("DB_PASSWORD is required")
	}
	if cfg.DbName == "" {
		return fmt.Errorf("DB_NAME is required")
	}

	return nil
}
