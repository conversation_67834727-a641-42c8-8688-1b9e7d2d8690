package main

import (
	"context"
	"fmt"
	"log"
	"os"
	"os/signal"
	"syscall"

	MQTT "github.com/eclipse/paho.mqtt.golang"
	"github.com/solarchapter-tech/water-iq-mqtt/config"
	"github.com/solarchapter-tech/water-iq-mqtt/topic"
)

func main() {
	// Create context for graceful shutdown
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// Initialize database
	config.DbInit()

	// Initialize application handlers
	app := topic.App()

	// Create connection manager
	connManager := config.NewConnectionManager(ctx)

	// Connect to MQTT broker
	if err := connManager.Connect(); err != nil {
		log.Fatalf("Failed to connect to MQTT broker: %v", err)
	}

	// Print connection success message
	connected()

	// Set up MQTT subscriptions
	if err := setupMQTTSubscriptions(connManager.GetClient(), app); err != nil {
		log.Fatalf("Failed to setup MQTT subscriptions: %v", err)
	}

	// Start connection monitoring and resubscription handling
	go handleConnectionEvents(ctx, connManager, app)

	// Set up graceful shutdown
	shutdown := make(chan os.Signal, 1)
	signal.Notify(shutdown, syscall.SIGINT, syscall.SIGTERM)

	// Wait for shutdown signal
	log.Println("Water IQ MQTT server is running. Press Ctrl+C to stop.")
	<-shutdown

	// Graceful shutdown
	log.Println("Shutting down gracefully...")
	gracefulShutdown(connManager, cancel)
}

// setupMQTTSubscriptions sets up all MQTT topic subscriptions
func setupMQTTSubscriptions(client MQTT.Client, app topic.AppModels) error {
	token := topic.Route(client, app)
	if token != nil {
		return fmt.Errorf("failed to setup MQTT routes: %w", token.Error())
	}
	return nil
}

// handleConnectionEvents handles connection lost and reconnection events
func handleConnectionEvents(ctx context.Context, connManager *config.ConnectionManager, app topic.AppModels) {
	for {
		select {
		case <-ctx.Done():
			log.Println("Connection event handler stopped")
			return

		case <-connManager.ConnectionLost():
			log.Println("Connection lost detected")

		case <-connManager.Reconnected():
			log.Println("Reconnection successful, resubscribing to topics...")
			if err := setupMQTTSubscriptions(connManager.GetClient(), app); err != nil {
				log.Printf("Failed to resubscribe after reconnection: %v", err)
				connManager.NotifyConnectionLost() // Trigger another reconnection attempt
			} else {
				log.Println("Successfully resubscribed to all topics")
			}
		}
	}
}

// gracefulShutdown performs cleanup operations before shutting down
func gracefulShutdown(connManager *config.ConnectionManager, cancel context.CancelFunc) {
	// Cancel context to signal shutdown to all components
	cancel()

	// Disconnect MQTT client gracefully
	if connManager != nil {
		connManager.Disconnect()
	}

	// Close database connection
	if db := config.DbManager(); db != nil {
		log.Println("Closing database connection...")
		if err := db.Close(); err != nil {
			log.Printf("Error closing database: %v", err)
		} else {
			log.Println("Database connection closed")
		}
	}

	log.Println("Shutdown complete")
}

// prints a short information when client is successfully connected.
func connected() {
	fmt.Println("=====================================================")
	fmt.Println("* * * HELLO FROM WATERIQ MONITORING SERVER MQTT * * *")
	fmt.Println("=====================================================")
}
