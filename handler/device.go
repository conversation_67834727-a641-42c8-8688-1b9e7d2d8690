package handler

import (
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	MQTT "github.com/eclipse/paho.mqtt.golang"
	"github.com/solarchapter-tech/water-iq-mqtt/model"
	"github.com/solarchapter-tech/water-iq-mqtt/service"
)

type DeviceHandler struct {
	DeviceService service.DeviceService
}

// Handler for device status log
func (deviceHandler *DeviceHandler) DeviceStatus(client MQTT.Client, msg MQTT.Message) {
	var req model.DeviceStatusReq
	payload := msg.Payload()

	if err := json.Unmarshal(payload, &req); err != nil {
		fmt.Printf("ERROR: Failed to unmarshal device status payload: %v, payload: %s\n", err, string(payload))
		return
	}

	if err := deviceHandler.DeviceService.DeviceStatus(&req); err != nil {
		fmt.Printf("ERROR: Failed to process device status: %v, request: %+v\n", err, req)
		return
	}

	fmt.Printf("INFO: Successfully processed device status for device: %s\n", req.DeviceCode)
}

// Handler for device status and telemetry
func (deviceHandler *DeviceHandler) DeviceTelemetry(client MQTT.Client, msg MQTT.Message) {
	topic := msg.Topic()
	payload := msg.Payload()

	req, ok := extractMACAddress(topic)
	if !ok {
		fmt.Printf("ERROR: Failed to extract MAC address from topic: %s\n", topic)
		// TODO: will send alert to email
		return
	}
	fmt.Printf("INFO: Processing telemetry for MAC address: %s\n", req.MacAddress)

	var err error
	req, err = parseTelemetryPayload(payload, req)
	if err != nil {
		fmt.Printf("ERROR: Failed to parse telemetry payload: %v, topic: %s, payload: %s\n", err, topic, string(payload))
		// TODO: will send alert to email
		return
	}

	req, err = enrichDevStas(req)
	if err != nil {
		fmt.Printf("ERROR: Failed to enrich DevStas data: %v, MAC: %s\n", err, req.MacAddress)
		// TODO: will send alert to email
		return
	}

	req, err = enrichCM3(req)
	if err != nil {
		fmt.Printf("ERROR: Failed to enrich CM3 data: %v, MAC: %s\n", err, req.MacAddress)
		// TODO: will send alert to email
		return
	}

	if err = deviceHandler.DeviceService.DeviceTelemetry(&req); err != nil {
		fmt.Printf("ERROR: Failed to save telemetry data: %v, MAC: %s\n", err, req.MacAddress)
		// TODO: will send alert to email
		return
	}

	fmt.Printf("INFO: Successfully processed telemetry data for MAC: %s, Level: %.2f, RSSI: %d, timestamp: %s\n",
		req.MacAddress, req.Params.CM3.SensorValue, req.Params.DevStas.RSSISignal, req.Params.CM3.Timestamp)
}

// extractMACAddress validates the topic format and returns the MAC address segment.
func extractMACAddress(topic string) (model.DeviceTelemetryReq, bool) {
	var req model.DeviceTelemetryReq
	parts := strings.Split(topic, "/")
	req.MacAddress = parts[len(parts)-1]
	return req, true
}

// parseTelemetryPayload unmarshals the incoming JSON payload.
func parseTelemetryPayload(payload []byte, req model.DeviceTelemetryReq) (model.DeviceTelemetryReq, error) {
	if err := json.Unmarshal(payload, &req); err != nil {
		return req, err
	}
	return req, nil
}

// enrichDevStas parses and enriches DevStas parameters if present.
func enrichDevStas(req model.DeviceTelemetryReq) (model.DeviceTelemetryReq, error) {
	if req.Params.DevStas.Value == "" {
		return req, nil
	}

	parts := strings.Split(req.Params.DevStas.Value, ",")
	if len(parts) < 4 {
		return req, fmt.Errorf("invalid DevStas value")
	}

	rssiSignal, err := strconv.Atoi(parts[1])
	if err != nil {
		return req, err
	}

	batteryVoltage, err := strconv.ParseFloat(parts[2], 64)
	if err != nil {
		return req, err
	}

	chipTemperature, err := strconv.ParseFloat(parts[3], 64)
	if err != nil {
		return req, err
	}

	req.Params.DevStas.SamplingInterval = parts[0]
	req.Params.DevStas.RSSISignal = int64(rssiSignal)
	req.Params.DevStas.BatteryVoltage = batteryVoltage
	req.Params.DevStas.ChipTemperature = chipTemperature

	return req, nil
}

// enrichCM3 parses and enriches CM3 parameters if present.
func enrichCM3(req model.DeviceTelemetryReq) (model.DeviceTelemetryReq, error) {
	if req.Params.CM3.Value == "" {
		return req, nil
	}

	parts := strings.Split(req.Params.CM3.Value, ",")
	if len(parts) < 3 {
		return req, fmt.Errorf("invalid CM3 value")
	}

	alarmType, err := strconv.Atoi(parts[0])
	if err != nil {
		return req, err
	}

	parsedTimestamp, err := time.Parse("2006-01-02 15:04:05", parts[1])
	if err != nil {
		return req, err
	}

	sensorValue, err := strconv.ParseFloat(parts[2], 64)
	if err != nil {
		return req, err
	}

	req.Params.CM3.AlarmType = alarmType
	req.Params.CM3.Timestamp = parts[1]
	req.Params.CM3.SensorValue = sensorValue
	req.Params.CM3.ParsedTimestamp = parsedTimestamp

	return req, nil
}
