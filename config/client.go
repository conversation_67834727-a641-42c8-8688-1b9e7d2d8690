package config

import (
	"context"
	"crypto/tls"
	"fmt"
	"log"
	"time"

	MQTT "github.com/eclipse/paho.mqtt.golang"
)

// MQTTClientConfig holds MQTT client configuration options
type MQTTClientConfig struct {
	ConnectTimeout    time.Duration
	KeepAlive         time.Duration
	PingTimeout       time.Duration
	WriteTimeout      time.Duration
	MaxReconnectDelay time.Duration
	AutoReconnect     bool
	CleanSession      bool
	QoS               byte
	EnableTLS         bool
}

// DefaultMQTTClientConfig returns default MQTT client configuration
func DefaultMQTTClientConfig() MQTTClientConfig {
	return MQTTClientConfig{
		ConnectTimeout:    30 * time.Second,
		KeepAlive:         60 * time.Second,
		PingTimeout:       10 * time.Second,
		WriteTimeout:      10 * time.Second,
		MaxReconnectDelay: 10 * time.Minute,
		AutoReconnect:     false,
		CleanSession:      true,
		QoS:               1, // At least once delivery
		EnableTLS:         false,
	}
}

// CreateClient returns a new MQTT client object with enhanced configuration.
func CreateClient(ctx context.Context) (MQTT.Client, error) {
	cfg := GetConfig()

	// Validate configuration
	if err := ValidateConfig(cfg); err != nil {
		return nil, fmt.Errorf("invalid configuration: %w", err)
	}

	mqttConfig := DefaultMQTTClientConfig()

	// Override defaults with configuration values
	mqttConfig.EnableTLS = cfg.MQTTEnableTLS == "true"

	// Parse QoS from string
	switch cfg.MQTTQoS {
	case "0":
		mqttConfig.QoS = 0
	case "1":
		mqttConfig.QoS = 1
	case "2":
		mqttConfig.QoS = 2
	}

	// Build broker URL with proper scheme
	scheme := "tcp"
	if mqttConfig.EnableTLS {
		scheme = "ssl"
	}
	brokerURL := fmt.Sprintf("%s://%s:%s", scheme, cfg.MQTTServer, cfg.MQTTPort)

	// Configure MQTT client options
	opts := MQTT.NewClientOptions().
		AddBroker(brokerURL).
		SetClientID(cfg.MQTTClientID).
		SetUsername(cfg.MQTTUsername).
		SetPassword(cfg.MQTTPassword).
		SetConnectTimeout(mqttConfig.ConnectTimeout).
		SetKeepAlive(mqttConfig.KeepAlive).
		SetPingTimeout(mqttConfig.PingTimeout).
		SetWriteTimeout(mqttConfig.WriteTimeout).
		SetMaxReconnectInterval(mqttConfig.MaxReconnectDelay).
		SetAutoReconnect(mqttConfig.AutoReconnect).
		SetCleanSession(mqttConfig.CleanSession)

	// Configure TLS if enabled
	if mqttConfig.EnableTLS {
		tlsConfig := &tls.Config{
			InsecureSkipVerify: false, // Set to true only for testing
		}
		opts.SetTLSConfig(tlsConfig)
	}

	// Set connection lost handler
	opts.SetConnectionLostHandler(func(client MQTT.Client, err error) {
		log.Printf("MQTT connection lost: %v", err)
	})

	// Set on connect handler
	opts.SetOnConnectHandler(func(client MQTT.Client) {
		log.Println("MQTT client connected successfully")
	})

	// Set reconnect handler
	opts.SetReconnectingHandler(func(client MQTT.Client, options *MQTT.ClientOptions) {
		log.Println("MQTT client attempting to reconnect...")
	})

	// Create client
	client := MQTT.NewClient(opts)

	// Connect with context timeout
	connectCtx, cancel := context.WithTimeout(ctx, mqttConfig.ConnectTimeout)
	defer cancel()

	// Connect in a goroutine to respect context cancellation
	connectDone := make(chan error, 1)
	go func() {
		if token := client.Connect(); token.Wait() && token.Error() != nil {
			connectDone <- token.Error()
		} else {
			connectDone <- nil
		}
	}()

	// Wait for connection or context cancellation
	select {
	case err := <-connectDone:
		if err != nil {
			return nil, fmt.Errorf("failed to connect to MQTT broker: %w", err)
		}
		return client, nil
	case <-connectCtx.Done():
		client.Disconnect(250) // Give 250ms for graceful disconnect
		return nil, fmt.Errorf("MQTT connection timeout: %w", connectCtx.Err())
	}
}
